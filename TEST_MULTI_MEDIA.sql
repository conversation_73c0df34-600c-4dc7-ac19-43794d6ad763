-- اختبار دعم الوسائط المتعددة
-- Test multi-media support

-- 1. التحقق من وجود عمود media_urls
SELECT 
    column_name, 
    data_type, 
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'posts' AND column_name = 'media_urls';

-- 2. عرض المنشورات التي تحتوي على media_urls
SELECT 
    id,
    content,
    type,
    media_url,
    media_urls,
    created_at
FROM posts 
WHERE media_urls IS NOT NULL 
ORDER BY created_at DESC 
LIMIT 10;

-- 3. عرض إحصائيات المنشورات
SELECT 
    type,
    COUNT(*) as total_posts,
    COUNT(CASE WHEN media_url IS NOT NULL THEN 1 END) as single_media,
    COUNT(CASE WHEN media_urls IS NOT NULL THEN 1 END) as multi_media
FROM posts 
GROUP BY type;

-- 4. إنشاء منشور اختبار مع وسائط متعددة
INSERT INTO posts (
    user_id,
    content,
    type,
    media_urls,
    created_at
) VALUES (
    '62fb0b2e-8cdd-4226-878f-3eec513192c', -- استبدل بمعرف المستخدم الحقيقي
    'منشور اختبار للوسائط المتعددة',
    'image',
    ARRAY['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
    NOW()
) RETURNING id, content, media_urls; 
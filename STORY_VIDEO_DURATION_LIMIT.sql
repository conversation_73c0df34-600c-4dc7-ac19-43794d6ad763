-- إضافة قيود على مدة الفيديو في القصص
-- Add video duration limits for stories

-- 1. إضافة عمود مدة الفيديو إلى جدول القصص
ALTER TABLE stories ADD COLUMN IF NOT EXISTS video_duration INTEGER;

-- 2. إن<PERSON>اء دالة للتحقق من مدة الفيديو
CREATE OR REPLACE FUNCTION check_video_duration()
RETURNS TRIGGER AS $$
BEGIN
    -- إذا كان نوع القصة فيديو، تحقق من المدة
    IF NEW.type = 'video' AND NEW.video_duration IS NOT NULL THEN
        -- إذا كان الفيديو أطول من دقيقتين (120 ثانية)، رفض الإدراج
        IF NEW.video_duration > 120 THEN
            RAISE EXCEPTION 'مدة الفيديو يجب أن تكون أقل من دقيقتين';
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 3. إنشاء trigger للتحقق من مدة الفيديو
DROP TRIGGER IF EXISTS check_video_duration_trigger ON stories;
CREATE TRIGGER check_video_duration_trigger
    BEFORE INSERT OR UPDATE ON stories
    FOR EACH ROW
    EXECUTE FUNCTION check_video_duration();

-- 4. إنشاء دالة لتحديث مدة الفيديو تلقائياً
CREATE OR REPLACE FUNCTION update_video_duration()
RETURNS TRIGGER AS $$
BEGIN
    -- إذا كان نوع القصة فيديو ولم يتم تحديد المدة، تعيين المدة الافتراضية
    IF NEW.type = 'video' AND NEW.video_duration IS NULL THEN
        NEW.video_duration := 60; -- دقيقة واحدة افتراضياً
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 5. إنشاء trigger لتحديث مدة الفيديو
DROP TRIGGER IF EXISTS update_video_duration_trigger ON stories;
CREATE TRIGGER update_video_duration_trigger
    BEFORE INSERT OR UPDATE ON stories
    FOR EACH ROW
    EXECUTE FUNCTION update_video_duration();

-- 6. تحديث القصص الموجودة
UPDATE stories 
SET video_duration = 60 
WHERE type = 'video' AND video_duration IS NULL;

-- 7. إنشاء index لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_stories_video_duration ON stories(video_duration) WHERE type = 'video';

-- 8. عرض القصص مع مدة الفيديو
SELECT 
    id,
    type,
    text,
    video_duration,
    CASE 
        WHEN type = 'video' AND video_duration > 60 THEN 'طويل'
        WHEN type = 'video' AND video_duration <= 60 THEN 'مناسب'
        ELSE 'غير فيديو'
    END as duration_status
FROM stories 
WHERE type = 'video'
ORDER BY created_at DESC
LIMIT 10;

-- 9. رسالة نجاح
SELECT 'تم إضافة قيود مدة الفيديو للقصص بنجاح!' as result; 
-- إصلاح مشكلة القصص مع شريط التقدم
-- Fix story progress bar issue

-- 1. التحقق من وجود جدول القصص
SELECT 
    table_name,
    column_name,
    data_type
FROM information_schema.columns 
WHERE table_name = 'stories'
ORDER BY ordinal_position;

-- 2. إضافة عمود مدة الفيديو إذا لم يكن موجوداً
ALTER TABLE stories ADD COLUMN IF NOT EXISTS video_duration INTEGER;

-- 3. تحديث مدة الفيديو للقصص الموجودة
UPDATE stories 
SET video_duration = 60 
WHERE type = 'video' AND video_duration IS NULL;

-- 4. إنشاء دالة للتحقق من مدة الفيديو
CREATE OR REPLACE FUNCTION check_video_duration()
RETURNS TRIGGER AS $$
BEGIN
    -- إذا كان نوع القصة فيديو، تحقق من المدة
    IF NEW.type = 'video' AND NEW.video_duration IS NOT NULL THEN
        -- إذا كان الفيديو أطول من دقيقتين (120 ثانية)، رفض الإدراج
        IF NEW.video_duration > 120 THEN
            RAISE EXCEPTION 'مدة الفيديو يجب أن تكون أقل من دقيقتين';
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 5. إنشاء trigger للتحقق من مدة الفيديو
DROP TRIGGER IF EXISTS check_video_duration_trigger ON stories;
CREATE TRIGGER check_video_duration_trigger
    BEFORE INSERT OR UPDATE ON stories
    FOR EACH ROW
    EXECUTE FUNCTION check_video_duration();

-- 6. إنشاء دالة لتحديث مدة الفيديو تلقائياً
CREATE OR REPLACE FUNCTION update_video_duration()
RETURNS TRIGGER AS $$
BEGIN
    -- إذا كان نوع القصة فيديو ولم يتم تحديد المدة، تعيين المدة الافتراضية
    IF NEW.type = 'video' AND NEW.video_duration IS NULL THEN
        NEW.video_duration := 60; -- دقيقة واحدة افتراضياً
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 7. إنشاء trigger لتحديث مدة الفيديو
DROP TRIGGER IF EXISTS update_video_duration_trigger ON stories;
CREATE TRIGGER update_video_duration_trigger
    BEFORE INSERT OR UPDATE ON stories
    FOR EACH ROW
    EXECUTE FUNCTION update_video_duration();

-- 8. إنشاء index لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_stories_video_duration ON stories(video_duration) WHERE type = 'video';

-- 9. عرض القصص مع مدة الفيديو
SELECT 
    id,
    type,
    text,
    video_duration,
    CASE 
        WHEN type = 'video' AND video_duration > 60 THEN 'طويل'
        WHEN type = 'video' AND video_duration <= 60 THEN 'مناسب'
        ELSE 'غير فيديو'
    END as duration_status
FROM stories 
WHERE type = 'video'
ORDER BY created_at DESC
LIMIT 10;

-- 10. عرض إحصائيات القصص
SELECT 
    'Story Statistics' as info,
    COUNT(*) as total_stories,
    COUNT(CASE WHEN type = 'text' THEN 1 END) as text_stories,
    COUNT(CASE WHEN type = 'image' THEN 1 END) as image_stories,
    COUNT(CASE WHEN type = 'video' THEN 1 END) as video_stories
FROM stories;

-- 11. رسالة نجاح
SELECT 'تم إصلاح مشكلة القصص مع شريط التقدم بنجاح!' as result; 
-- تحديث المستخدم كموثق وإضافة الأعمدة المطلوبة
-- قم بتنفيذ هذه الأوامر في Supabase SQL Editor

-- إضافة عمود verification_status إذا لم يكن موجوداً
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'profiles' AND column_name = 'verification_status') THEN
        ALTER TABLE profiles ADD COLUMN verification_status TEXT DEFAULT NULL;
    END IF;
END $$;

-- إضافة عمود verification_requested_at إذا لم يكن موجوداً
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'profiles' AND column_name = 'verification_requested_at') THEN
        ALTER TABLE profiles ADD COLUMN verification_requested_at TIMESTAMP WITH TIME ZONE DEFAULT NULL;
    END IF;
END $$;

-- تحديث المستخدم المحدد ليكون موثقاً
UPDATE profiles
SET
    is_verified = true,
    verification_status = 'approved',
    verification_requested_at = NOW()
WHERE id = '62fb0b2e-8cdd-4226-878f-3eec513192c';

-- التحقق من التحديث
SELECT id, name, is_verified, verification_status, verification_requested_at 
FROM profiles 
WHERE id = '62fb0b2e-8cdd-4226-878f-3eec513192c'; 
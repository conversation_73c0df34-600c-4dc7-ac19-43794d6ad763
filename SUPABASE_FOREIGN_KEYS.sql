-- =============================================================
--  إضافة المراجع الخارجية لـ Supabase (اختياري)
--  Supabase Foreign Keys (Optional)
-- =============================================================

-- تحذير: شغّل هذا الملف فقط إذا كانت الجداول فارغة
-- أو إذا كنت متأكداً من صحة البيانات

-- 1. إضافة مرجع space_id في space_posts
ALTER TABLE space_posts 
ADD CONSTRAINT space_posts_space_id_fkey 
FOREIGN KEY (space_id) REFERENCES spaces(id) ON DELETE CASCADE;

-- 2. إضافة مرجع post_id في space_post_likes
ALTER TABLE space_post_likes 
ADD CONSTRAINT space_post_likes_post_id_fkey 
FOREIGN KEY (post_id) REFERENCES space_posts(id) ON DELETE CASCADE;

-- 3. إضافة مرجع post_id في space_post_comments
ALTER TABLE space_post_comments 
ADD CONSTRAINT space_post_comments_post_id_fkey 
FOREIGN KEY (post_id) REFERENCES space_posts(id) ON DELETE CASCADE;

-- 4. إضافة قيد الفرادة للإعجابات
ALTER TABLE space_post_likes 
ADD CONSTRAINT space_post_likes_unique 
UNIQUE(post_id, user_id);
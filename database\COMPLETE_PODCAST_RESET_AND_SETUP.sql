-- =============================================================
--  حذف وإعادة إنشاء نظام البودكاست بالكامل - أرزاوو
--  Complete Podcast System Reset and Setup - Arzawo
-- =============================================================

-- ⚠️ تحذير: هذا الملف سيحذف جميع بيانات البودكاست الموجودة!
-- Warning: This file will delete all existing podcast data!

-- =============================================================
--  الخطوة 1: حذف جميع السياسات الموجودة
-- =============================================================

-- حذف سياسات Storage
DROP POLICY IF EXISTS "Authenticated users can upload audio episodes" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can view audio episodes" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own audio episodes" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload podcast covers" ON storage.objects;
DROP POLICY IF EXISTS "Anyone can view podcast covers" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own podcast covers" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload voice comments" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can view voice comments" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own voice comments" ON storage.objects;

-- حذف سياسات الجداول
DROP POLICY IF EXISTS "Users can view public podcasts" ON audio_podcasts;
DROP POLICY IF EXISTS "Users can create their own podcasts" ON audio_podcasts;
DROP POLICY IF EXISTS "Users can update their own podcasts" ON audio_podcasts;
DROP POLICY IF EXISTS "Users can delete their own podcasts" ON audio_podcasts;
DROP POLICY IF EXISTS "Users can view published episodes" ON audio_episodes;
DROP POLICY IF EXISTS "Podcast owners can manage episodes" ON audio_episodes;
DROP POLICY IF EXISTS "Users can manage their own follows" ON audio_podcast_followers;
DROP POLICY IF EXISTS "Users can manage their own likes" ON audio_podcast_likes;
DROP POLICY IF EXISTS "Users can manage their own episode likes" ON audio_episode_likes;
DROP POLICY IF EXISTS "Users can track their own listens" ON audio_episode_listens;
DROP POLICY IF EXISTS "Users can view all comments" ON audio_episode_text_comments;
DROP POLICY IF EXISTS "Users can manage their own text comments" ON audio_episode_text_comments;
DROP POLICY IF EXISTS "Users can update their own text comments" ON audio_episode_text_comments;
DROP POLICY IF EXISTS "Users can delete their own text comments" ON audio_episode_text_comments;
DROP POLICY IF EXISTS "Users can view all voice comments" ON audio_episode_voice_comments;
DROP POLICY IF EXISTS "Users can manage their own voice comments" ON audio_episode_voice_comments;
DROP POLICY IF EXISTS "Users can delete their own voice comments" ON audio_episode_voice_comments;

-- =============================================================
--  الخطوة 2: حذف جميع الجداول الموجودة
-- =============================================================

-- حذف الجداول بالترتيب الصحيح (من التابع إلى الرئيسي)
DROP TABLE IF EXISTS audio_episode_voice_comments CASCADE;
DROP TABLE IF EXISTS audio_episode_text_comments CASCADE;
DROP TABLE IF EXISTS audio_episode_listens CASCADE;
DROP TABLE IF EXISTS audio_episode_likes CASCADE;
DROP TABLE IF EXISTS audio_episode_favorites CASCADE;
DROP TABLE IF EXISTS audio_podcast_likes CASCADE;
DROP TABLE IF EXISTS audio_podcast_followers CASCADE;
DROP TABLE IF EXISTS audio_episodes CASCADE;
DROP TABLE IF EXISTS audio_podcasts CASCADE;

-- حذف Views إن وجدت
DROP VIEW IF EXISTS featured_audio_podcasts CASCADE;
DROP VIEW IF EXISTS popular_audio_podcasts CASCADE;
DROP VIEW IF EXISTS latest_audio_episodes CASCADE;
DROP VIEW IF EXISTS audio_podcasts_with_stats CASCADE;
DROP VIEW IF EXISTS audio_episodes_with_podcast CASCADE;

-- =============================================================
--  الخطوة 3: حذف Storage Buckets
-- =============================================================

-- حذف جميع الملفات من Buckets
DELETE FROM storage.objects WHERE bucket_id IN ('audio-episodes', 'podcast-covers', 'voice-comments');

-- حذف Buckets
DELETE FROM storage.buckets WHERE id IN ('audio-episodes', 'podcast-covers', 'voice-comments');

-- =============================================================
--  الخطوة 4: إنشاء Storage Buckets الجديدة
-- =============================================================

-- 1. إنشاء bucket للملفات الصوتية
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'audio-episodes',
    'audio-episodes',
    false, -- خاص للحماية
    104857600, -- 100MB حد أقصى
    ARRAY['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/ogg', 'audio/m4a', 'audio/aac']
);

-- 2. إنشاء bucket لصور أغلفة البودكاست
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'podcast-covers',
    'podcast-covers',
    true, -- عام للعرض
    5242880, -- 5MB حد أقصى
    ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
);

-- 3. إنشاء bucket للتعليقات الصوتية
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'voice-comments',
    'voice-comments',
    false, -- خاص للحماية
    10485760, -- 10MB حد أقصى
    ARRAY['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/ogg', 'audio/m4a', 'audio/aac']
);

-- =============================================================
--  الخطوة 5: إنشاء الجداول الجديدة
-- =============================================================

-- جدول البودكاستات الرئيسي
CREATE TABLE audio_podcasts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    creator_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    cover_image_url TEXT,
    category VARCHAR(50) DEFAULT 'general',
    language VARCHAR(10) DEFAULT 'ar',
    privacy_setting VARCHAR(20) DEFAULT 'public',
    content_rating VARCHAR(20) DEFAULT 'general',
    allow_comments BOOLEAN DEFAULT true,
    allow_downloads BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,
    total_episodes INTEGER DEFAULT 0,
    total_listens BIGINT DEFAULT 0,
    total_followers INTEGER DEFAULT 0,
    total_likes INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول الحلقات الصوتية
CREATE TABLE audio_episodes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    podcast_id UUID REFERENCES audio_podcasts(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    audio_url TEXT NOT NULL,
    duration_seconds INTEGER DEFAULT 0,
    file_size_bytes BIGINT DEFAULT 0,
    is_published BOOLEAN DEFAULT false,
    publish_at TIMESTAMP WITH TIME ZONE,
    scheduled_for TIMESTAMP WITH TIME ZONE,
    listens_count INTEGER DEFAULT 0,
    likes_count INTEGER DEFAULT 0,
    comments_count INTEGER DEFAULT 0,
    shares_count INTEGER DEFAULT 0,
    downloads_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول متابعة البودكاستات
CREATE TABLE audio_podcast_followers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    podcast_id UUID REFERENCES audio_podcasts(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    notifications_enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(podcast_id, user_id)
);

-- جدول إعجابات البودكاستات
CREATE TABLE audio_podcast_likes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    podcast_id UUID REFERENCES audio_podcasts(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(podcast_id, user_id)
);

-- جدول إعجابات الحلقات
CREATE TABLE audio_episode_likes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    episode_id UUID REFERENCES audio_episodes(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(episode_id, user_id)
);

-- جدول إحصائيات الاستماع
CREATE TABLE audio_episode_listens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    episode_id UUID REFERENCES audio_episodes(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    listen_duration INTEGER DEFAULT 0,
    completed BOOLEAN DEFAULT false,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول التعليقات النصية
CREATE TABLE audio_episode_text_comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    episode_id UUID REFERENCES audio_episodes(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    parent_comment_id UUID REFERENCES audio_episode_text_comments(id) ON DELETE CASCADE,
    likes_count INTEGER DEFAULT 0,
    replies_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول التعليقات الصوتية
CREATE TABLE audio_episode_voice_comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    episode_id UUID REFERENCES audio_episodes(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    audio_url TEXT NOT NULL,
    duration_seconds INTEGER DEFAULT 0,
    parent_comment_id UUID REFERENCES audio_episode_voice_comments(id) ON DELETE CASCADE,
    likes_count INTEGER DEFAULT 0,
    replies_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول المفضلة
CREATE TABLE audio_episode_favorites (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    episode_id UUID REFERENCES audio_episodes(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(episode_id, user_id)
);

-- =============================================================
--  الخطوة 6: إنشاء الفهارس للأداء
-- =============================================================

-- فهارس البودكاستات
CREATE INDEX idx_audio_podcasts_creator ON audio_podcasts(creator_id);
CREATE INDEX idx_audio_podcasts_category ON audio_podcasts(category);
CREATE INDEX idx_audio_podcasts_created_at ON audio_podcasts(created_at DESC);
CREATE INDEX idx_audio_podcasts_featured ON audio_podcasts(is_featured, total_listens DESC);
CREATE INDEX idx_audio_podcasts_privacy ON audio_podcasts(privacy_setting);

-- فهارس الحلقات
CREATE INDEX idx_audio_episodes_podcast ON audio_episodes(podcast_id);
CREATE INDEX idx_audio_episodes_published ON audio_episodes(is_published, publish_at DESC);
CREATE INDEX idx_audio_episodes_created_at ON audio_episodes(created_at DESC);
CREATE INDEX idx_audio_episodes_listens ON audio_episodes(listens_count DESC);

-- فهارس المتابعة والإعجابات
CREATE INDEX idx_audio_podcast_followers_user ON audio_podcast_followers(user_id);
CREATE INDEX idx_audio_podcast_followers_podcast ON audio_podcast_followers(podcast_id);
CREATE INDEX idx_audio_podcast_likes_user ON audio_podcast_likes(user_id);
CREATE INDEX idx_audio_podcast_likes_podcast ON audio_podcast_likes(podcast_id);
CREATE INDEX idx_audio_episode_likes_user ON audio_episode_likes(user_id);
CREATE INDEX idx_audio_episode_likes_episode ON audio_episode_likes(episode_id);

-- فهارس الاستماع والتعليقات
CREATE INDEX idx_audio_episode_listens_episode ON audio_episode_listens(episode_id);
CREATE INDEX idx_audio_episode_listens_user ON audio_episode_listens(user_id);
CREATE INDEX idx_audio_episode_text_comments_episode ON audio_episode_text_comments(episode_id);
CREATE INDEX idx_audio_episode_text_comments_user ON audio_episode_text_comments(user_id);
CREATE INDEX idx_audio_episode_voice_comments_episode ON audio_episode_voice_comments(episode_id);
CREATE INDEX idx_audio_episode_voice_comments_user ON audio_episode_voice_comments(user_id);
CREATE INDEX idx_audio_episode_favorites_user ON audio_episode_favorites(user_id);
CREATE INDEX idx_audio_episode_favorites_episode ON audio_episode_favorites(episode_id);

-- =============================================================
--  الخطوة 7: إنشاء سياسات الأمان (RLS)
-- =============================================================

-- تفعيل RLS على جميع الجداول
ALTER TABLE audio_podcasts ENABLE ROW LEVEL SECURITY;
ALTER TABLE audio_episodes ENABLE ROW LEVEL SECURITY;
ALTER TABLE audio_podcast_followers ENABLE ROW LEVEL SECURITY;
ALTER TABLE audio_podcast_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE audio_episode_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE audio_episode_listens ENABLE ROW LEVEL SECURITY;
ALTER TABLE audio_episode_text_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE audio_episode_voice_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE audio_episode_favorites ENABLE ROW LEVEL SECURITY;

-- سياسات البودكاستات
CREATE POLICY "Users can view public podcasts" ON audio_podcasts
    FOR SELECT USING (
        privacy_setting = 'public'
        OR creator_id = auth.uid()
        OR auth.uid() IN (
            SELECT user_id FROM audio_podcast_followers
            WHERE podcast_id = id
        )
    );

CREATE POLICY "Users can create their own podcasts" ON audio_podcasts
    FOR INSERT WITH CHECK (creator_id = auth.uid());

CREATE POLICY "Users can update their own podcasts" ON audio_podcasts
    FOR UPDATE USING (creator_id = auth.uid());

CREATE POLICY "Users can delete their own podcasts" ON audio_podcasts
    FOR DELETE USING (creator_id = auth.uid());

-- سياسات الحلقات
CREATE POLICY "Users can view published episodes" ON audio_episodes
    FOR SELECT USING (
        (is_published = true AND publish_at <= NOW())
        OR EXISTS (
            SELECT 1 FROM audio_podcasts
            WHERE id = podcast_id AND creator_id = auth.uid()
        )
        OR EXISTS (
            SELECT 1 FROM audio_podcasts p
            JOIN audio_podcast_followers f ON p.id = f.podcast_id
            WHERE p.id = podcast_id AND f.user_id = auth.uid()
        )
    );

CREATE POLICY "Podcast owners can manage episodes" ON audio_episodes
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM audio_podcasts
            WHERE id = podcast_id AND creator_id = auth.uid()
        )
    );

-- سياسات المتابعة والإعجابات
CREATE POLICY "Users can view all follows" ON audio_podcast_followers
    FOR SELECT USING (true);

CREATE POLICY "Users can manage their own follows" ON audio_podcast_followers
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can delete their own follows" ON audio_podcast_followers
    FOR DELETE USING (user_id = auth.uid());

CREATE POLICY "Users can view all podcast likes" ON audio_podcast_likes
    FOR SELECT USING (true);

CREATE POLICY "Users can manage their own podcast likes" ON audio_podcast_likes
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can delete their own podcast likes" ON audio_podcast_likes
    FOR DELETE USING (user_id = auth.uid());

CREATE POLICY "Users can view all episode likes" ON audio_episode_likes
    FOR SELECT USING (true);

CREATE POLICY "Users can manage their own episode likes" ON audio_episode_likes
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can delete their own episode likes" ON audio_episode_likes
    FOR DELETE USING (user_id = auth.uid());

-- سياسات الاستماع
CREATE POLICY "Users can track listens" ON audio_episode_listens
    FOR INSERT WITH CHECK (user_id = auth.uid() OR user_id IS NULL);

CREATE POLICY "Users can view their own listens" ON audio_episode_listens
    FOR SELECT USING (user_id = auth.uid() OR user_id IS NULL);

-- سياسات التعليقات النصية
CREATE POLICY "Users can view all text comments" ON audio_episode_text_comments
    FOR SELECT USING (true);

CREATE POLICY "Users can create text comments" ON audio_episode_text_comments
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own text comments" ON audio_episode_text_comments
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own text comments" ON audio_episode_text_comments
    FOR DELETE USING (user_id = auth.uid());

-- سياسات التعليقات الصوتية
CREATE POLICY "Users can view all voice comments" ON audio_episode_voice_comments
    FOR SELECT USING (true);

CREATE POLICY "Users can create voice comments" ON audio_episode_voice_comments
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can delete their own voice comments" ON audio_episode_voice_comments
    FOR DELETE USING (user_id = auth.uid());

-- سياسات المفضلة
CREATE POLICY "Users can view their own favorites" ON audio_episode_favorites
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can manage their own favorites" ON audio_episode_favorites
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can delete their own favorites" ON audio_episode_favorites
    FOR DELETE USING (user_id = auth.uid());

-- =============================================================
--  الخطوة 8: إنشاء سياسات Storage
-- =============================================================

-- سياسات رفع الملفات الصوتية
CREATE POLICY "Authenticated users can upload audio episodes" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'audio-episodes' AND
        auth.role() = 'authenticated'
    );

CREATE POLICY "Authenticated users can view audio episodes" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'audio-episodes' AND
        auth.role() = 'authenticated'
    );

CREATE POLICY "Users can delete their own audio episodes" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'audio-episodes' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- سياسات صور الأغلفة
CREATE POLICY "Authenticated users can upload podcast covers" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'podcast-covers' AND
        auth.role() = 'authenticated'
    );

CREATE POLICY "Anyone can view podcast covers" ON storage.objects
    FOR SELECT USING (bucket_id = 'podcast-covers');

CREATE POLICY "Users can delete their own podcast covers" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'podcast-covers' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- سياسات التعليقات الصوتية
CREATE POLICY "Authenticated users can upload voice comments" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'voice-comments' AND
        auth.role() = 'authenticated'
    );

CREATE POLICY "Authenticated users can view voice comments" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'voice-comments' AND
        auth.role() = 'authenticated'
    );

CREATE POLICY "Users can delete their own voice comments" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'voice-comments' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- =============================================================
--  الخطوة 9: إنشاء الدوال المساعدة والمحفزات
-- =============================================================

-- دالة تحديث إحصائيات البودكاست
CREATE OR REPLACE FUNCTION update_podcast_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- تحديث عدد الحلقات
    UPDATE audio_podcasts
    SET total_episodes = (
        SELECT COUNT(*)
        FROM audio_episodes
        WHERE podcast_id = COALESCE(NEW.podcast_id, OLD.podcast_id)
        AND is_published = true
    ),
    updated_at = NOW()
    WHERE id = COALESCE(NEW.podcast_id, OLD.podcast_id);

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- دالة تحديث إحصائيات الحلقة
CREATE OR REPLACE FUNCTION update_episode_stats()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_TABLE_NAME = 'audio_episode_likes' THEN
        UPDATE audio_episodes
        SET likes_count = (
            SELECT COUNT(*)
            FROM audio_episode_likes
            WHERE episode_id = COALESCE(NEW.episode_id, OLD.episode_id)
        )
        WHERE id = COALESCE(NEW.episode_id, OLD.episode_id);
    END IF;

    IF TG_TABLE_NAME = 'audio_episode_text_comments' OR TG_TABLE_NAME = 'audio_episode_voice_comments' THEN
        UPDATE audio_episodes
        SET comments_count = (
            SELECT COUNT(*)
            FROM audio_episode_text_comments
            WHERE episode_id = COALESCE(NEW.episode_id, OLD.episode_id)
        ) + (
            SELECT COUNT(*)
            FROM audio_episode_voice_comments
            WHERE episode_id = COALESCE(NEW.episode_id, OLD.episode_id)
        )
        WHERE id = COALESCE(NEW.episode_id, OLD.episode_id);
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- دالة تحديث إحصائيات الاستماع
CREATE OR REPLACE FUNCTION update_listen_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- تحديث عدد الاستماعات للحلقة
    UPDATE audio_episodes
    SET listens_count = (
        SELECT COUNT(*)
        FROM audio_episode_listens
        WHERE episode_id = NEW.episode_id
    )
    WHERE id = NEW.episode_id;

    -- تحديث إجمالي الاستماعات للبودكاست
    UPDATE audio_podcasts
    SET total_listens = (
        SELECT COALESCE(SUM(e.listens_count), 0)
        FROM audio_episodes e
        WHERE e.podcast_id = (
            SELECT podcast_id FROM audio_episodes WHERE id = NEW.episode_id
        )
    )
    WHERE id = (
        SELECT podcast_id FROM audio_episodes WHERE id = NEW.episode_id
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- دالة تحديث إحصائيات المتابعة
CREATE OR REPLACE FUNCTION update_follower_stats()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE audio_podcasts
    SET total_followers = (
        SELECT COUNT(*)
        FROM audio_podcast_followers
        WHERE podcast_id = COALESCE(NEW.podcast_id, OLD.podcast_id)
    )
    WHERE id = COALESCE(NEW.podcast_id, OLD.podcast_id);

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- =============================================================
--  الخطوة 10: إنشاء المحفزات
-- =============================================================

-- محفزات تحديث إحصائيات البودكاست
CREATE TRIGGER trigger_update_podcast_stats_on_episode_change
    AFTER INSERT OR UPDATE OR DELETE ON audio_episodes
    FOR EACH ROW EXECUTE FUNCTION update_podcast_stats();

-- محفزات تحديث إحصائيات الحلقة
CREATE TRIGGER trigger_update_episode_likes_stats
    AFTER INSERT OR DELETE ON audio_episode_likes
    FOR EACH ROW EXECUTE FUNCTION update_episode_stats();

CREATE TRIGGER trigger_update_episode_text_comments_stats
    AFTER INSERT OR DELETE ON audio_episode_text_comments
    FOR EACH ROW EXECUTE FUNCTION update_episode_stats();

CREATE TRIGGER trigger_update_episode_voice_comments_stats
    AFTER INSERT OR DELETE ON audio_episode_voice_comments
    FOR EACH ROW EXECUTE FUNCTION update_episode_stats();

-- محفز تحديث إحصائيات الاستماع
CREATE TRIGGER trigger_update_listen_stats
    AFTER INSERT ON audio_episode_listens
    FOR EACH ROW EXECUTE FUNCTION update_listen_stats();

-- محفز تحديث إحصائيات المتابعة
CREATE TRIGGER trigger_update_follower_stats
    AFTER INSERT OR DELETE ON audio_podcast_followers
    FOR EACH ROW EXECUTE FUNCTION update_follower_stats();

-- محفز تحديث إحصائيات الإعجابات
CREATE TRIGGER trigger_update_podcast_likes_stats
    AFTER INSERT OR DELETE ON audio_podcast_likes
    FOR EACH ROW EXECUTE FUNCTION update_follower_stats();

-- =============================================================
--  الخطوة 11: إنشاء Views مفيدة
-- =============================================================

-- View للبودكاستات مع الإحصائيات
CREATE VIEW audio_podcasts_with_stats AS
SELECT
    p.*,
    COALESCE(p.total_episodes, 0) as episodes_count,
    COALESCE(p.total_listens, 0) as listens_count,
    COALESCE(p.total_followers, 0) as followers_count,
    COALESCE(p.total_likes, 0) as likes_count,
    EXISTS(
        SELECT 1 FROM audio_podcast_followers f
        WHERE f.podcast_id = p.id AND f.user_id = auth.uid()
    ) as is_following,
    EXISTS(
        SELECT 1 FROM audio_podcast_likes l
        WHERE l.podcast_id = p.id AND l.user_id = auth.uid()
    ) as is_liked
FROM audio_podcasts p;

-- View للبودكاستات المميزة
CREATE VIEW featured_audio_podcasts AS
SELECT * FROM audio_podcasts_with_stats
WHERE is_featured = true
ORDER BY total_listens DESC, created_at DESC;

-- View للبودكاستات الشائعة
CREATE VIEW popular_audio_podcasts AS
SELECT * FROM audio_podcasts_with_stats
WHERE privacy_setting = 'public'
ORDER BY total_listens DESC, total_followers DESC, created_at DESC;

-- View لأحدث الحلقات
CREATE VIEW latest_audio_episodes AS
SELECT
    e.*,
    p.name as podcast_name,
    p.cover_image_url as podcast_cover,
    p.creator_id as podcast_creator_id,
    EXISTS(
        SELECT 1 FROM audio_episode_likes l
        WHERE l.episode_id = e.id AND l.user_id = auth.uid()
    ) as is_liked,
    EXISTS(
        SELECT 1 FROM audio_episode_favorites f
        WHERE f.episode_id = e.id AND f.user_id = auth.uid()
    ) as is_favorited
FROM audio_episodes e
JOIN audio_podcasts p ON e.podcast_id = p.id
WHERE e.is_published = true
AND (e.publish_at IS NULL OR e.publish_at <= NOW())
AND p.privacy_setting = 'public'
ORDER BY e.created_at DESC;

-- =============================================================
--  الخطوة 12: إدراج بيانات تجريبية
-- =============================================================

-- إدراج بودكاست تجريبي (فقط إذا كان هناك مستخدم مسجل)
DO $$
DECLARE
    test_user_id UUID;
    test_podcast_id UUID;
BEGIN
    -- البحث عن أول مستخدم مسجل
    SELECT id INTO test_user_id FROM auth.users LIMIT 1;

    IF test_user_id IS NOT NULL THEN
        -- إنشاء بودكاست تجريبي
        INSERT INTO audio_podcasts (
            creator_id,
            name,
            description,
            category,
            language,
            is_featured
        ) VALUES (
            test_user_id,
            'بودكاست أرزاوو التجريبي',
            'هذا بودكاست تجريبي لاختبار جميع مميزات النظام الجديد',
            'technology',
            'ar',
            true
        ) RETURNING id INTO test_podcast_id;

        -- إنشاء حلقة تجريبية
        INSERT INTO audio_episodes (
            podcast_id,
            title,
            description,
            audio_url,
            duration_seconds,
            is_published,
            publish_at
        ) VALUES (
            test_podcast_id,
            'الحلقة الأولى - مرحباً بكم',
            'حلقة ترحيبية لاختبار النظام',
            'https://example.com/test-episode.mp3',
            300,
            true,
            NOW()
        );

        RAISE NOTICE 'تم إنشاء بودكاست تجريبي بنجاح!';
    END IF;
END $$;

-- =============================================================
--  رسالة النجاح النهائية
-- =============================================================

SELECT '🎉 تم إعداد نظام البودكاست بالكامل بنجاح!
✅ تم حذف جميع البيانات القديمة
✅ تم إنشاء جميع الجداول والفهارس
✅ تم تطبيق جميع سياسات الأمان
✅ تم إعداد Storage بالكامل
✅ تم إنشاء الدوال والمحفزات
✅ تم إنشاء Views مفيدة
✅ تم إدراج بيانات تجريبية

يمكنك الآن استخدام جميع مميزات البودكاست بدون أي مشاكل!' as "🎙️ نظام البودكاست جاهز";

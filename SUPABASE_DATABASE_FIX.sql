-- =============================================================
--  إصلاح قاعدة البيانات لـ Supabase - بدون transactions
--  Supabase Database Fix - No Transactions
-- =============================================================

-- 1. إنشاء جدول spaces الأساسي
CREATE TABLE IF NOT EXISTS spaces (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    privacy TEXT DEFAULT 'public',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. إنشاء جدول space_posts
CREATE TABLE IF NOT EXISTS space_posts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    space_id UUID NOT NULL,
    author_id UUID NOT NULL,
    content TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    likes_count INTEGER DEFAULT 0,
    comments_count INTEGER DEFAULT 0
);

-- 3. إنشاء جدول space_post_likes
CREATE TABLE IF NOT EXISTS space_post_likes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    post_id UUID NOT NULL,
    user_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. إنشاء جدول space_post_comments
CREATE TABLE IF NOT EXISTS space_post_comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    post_id UUID NOT NULL,
    user_id UUID NOT NULL,
    content TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. إنشاء فهارس أساسية
CREATE INDEX IF NOT EXISTS idx_space_posts_space_id ON space_posts(space_id);
CREATE INDEX IF NOT EXISTS idx_space_posts_author_id ON space_posts(author_id);
CREATE INDEX IF NOT EXISTS idx_space_post_likes_post_id ON space_post_likes(post_id);
CREATE INDEX IF NOT EXISTS idx_space_post_comments_post_id ON space_post_comments(post_id);
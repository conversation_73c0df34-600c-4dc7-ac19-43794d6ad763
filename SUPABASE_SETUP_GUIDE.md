# 🚀 دليل إعداد Supabase - خطوة بخطوة

## 📋 **الخطوات المطلوبة**

### **الخطوة 1: الإصلاح الأساسي** ⭐
```sql
-- في Supabase SQL Editor
-- انسخ والصق محتوى الملف التالي:
SUPABASE_DATABASE_FIX.sql
```

### **الخطوة 2: إصلاح نظام التصويت** 🗳️
```sql
-- إذا كان لديك جداول التصويت (polls, poll_options, poll_votes)
-- انسخ والصق محتوى الملف التالي:
SUPABASE_POLL_FIX.sql
```

### **الخطوة 3: المراجع الخارجية (اختياري)** 🔗
```sql
-- فقط إذا كانت الجداول فارغة أو تريد قيود صارمة
-- انسخ والصق محتوى الملف التالي:
SUPABASE_FOREIGN_KEYS.sql
```

## ✅ **التحقق من نجاح الإعداد**

### **اختبار الجداول:**
```sql
-- تحقق من وجود الجداول
SELECT table_name 
FROM information_schema.tables 
WHERE table_name LIKE 'space%';
```

### **اختبار إدراج البيانات:**
```sql
-- اختبار إنشاء مساحة
INSERT INTO spaces (name, description) 
VALUES ('مساحة اختبار', 'وصف تجريبي');

-- اختبار إنشاء منشور
INSERT INTO space_posts (space_id, author_id, content) 
VALUES (
    (SELECT id FROM spaces LIMIT 1),
    auth.uid(),
    'منشور تجريبي'
);
```

### **اختبار قراءة البيانات:**
```sql
SELECT * FROM spaces;
SELECT * FROM space_posts;
```

## 🚨 **استكشاف الأخطاء**

### **خطأ: syntax error at or near "BEGIN"**
- ❌ **السبب**: Supabase لا يدعم transactions في SQL Editor
- ✅ **الحل**: استخدم `SUPABASE_DATABASE_FIX.sql` بدلاً من الملفات الأخرى

### **خطأ: relation does not exist**
- ❌ **السبب**: الجدول غير موجود
- ✅ **الحل**: شغّل `SUPABASE_DATABASE_FIX.sql` أولاً

### **خطأ: column does not exist**
- ❌ **السبب**: العمود المطلوب غير موجود
- ✅ **الحل**: تحقق من أسماء الأعمدة في الكود

## 📊 **ما سيتم إنشاؤه**

### **الجداول:**
1. ✅ `spaces` - المساحات
2. ✅ `space_posts` - منشورات المساحات
3. ✅ `space_post_likes` - إعجابات المنشورات
4. ✅ `space_post_comments` - تعليقات المنشورات

### **الفهارس:**
1. ✅ `idx_space_posts_space_id`
2. ✅ `idx_space_posts_author_id`
3. ✅ `idx_space_post_likes_post_id`
4. ✅ `idx_space_post_comments_post_id`

### **الدوال (إذا شغّلت ملف التصويت):**
1. ✅ `update_poll_votes()` - تحديث أصوات التصويت
2. ✅ `poll_votes_trigger` - trigger للتحديث التلقائي

## 🎯 **النتيجة المتوقعة**

### **بعد الخطوة 1:**
- ✅ الجداول الأساسية موجودة
- ✅ يمكن إدراج البيانات
- ✅ التطبيق يعمل بدون أخطاء

### **بعد الخطوة 2:**
- ✅ نظام التصويت يعمل
- ✅ تحديث الأصوات تلقائياً

### **بعد الخطوة 3:**
- ✅ قيود البيانات محكمة
- ✅ حماية من البيانات المعطوبة

## 💡 **نصائح مهمة**

### **للمبتدئين:**
- شغّل الخطوة 1 فقط
- اختبر التطبيق
- إذا عمل بشكل جيد، انتقل للخطوات التالية

### **للمطورين المتقدمين:**
- شغّل جميع الخطوات
- أضف RLS policies حسب الحاجة
- راقب الأداء والأخطاء

## 🚀 **بعد الانتهاء**

### **اختبر التطبيق:**
```bash
flutter run
```

### **تحقق من الوظائف:**
- ✅ إنشاء المساحات
- ✅ إنشاء المنشورات
- ✅ الإعجاب والتعليق
- ✅ نظام التصويت

**مبروك! قاعدة البيانات جاهزة للاستخدام** 🎉
-- =============================================================
--  إصلاح نظام التصويت لـ Supabase
--  Supabase Poll System Fix
-- =============================================================

-- 1. دالة بسيطة لتحديث الأصوات
CREATE OR REPLACE FUNCTION update_poll_votes()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE poll_options 
        SET votes = (SELECT COUNT(*) FROM poll_votes WHERE option_id = NEW.option_id)
        WHERE id = NEW.option_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE poll_options 
        SET votes = (SELECT COUNT(*) FROM poll_votes WHERE option_id = OLD.option_id)
        WHERE id = OLD.option_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 2. حذف trigger القديم إذا كان موجوداً
DROP TRIGGER IF EXISTS poll_votes_trigger ON poll_votes;

-- 3. إنشاء trigger جديد
CREATE TRIGGER poll_votes_trigger
    AFTER INSERT OR DELETE ON poll_votes
    FOR EACH ROW
    EXECUTE FUNCTION update_poll_votes();